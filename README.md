# Digital Wallet API

A Node.js/Express API for digital wallet management with TypeScript, MongoDB, and JWT authentication.

## Features

- User authentication and authorization
- Wallet management
- Transaction processing
- Agent commission system
- Daily and monthly transaction limits

## Local Development

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env` file based on `.env.example`:
   ```bash
   cp .env.example .env
   ```

4. Update the environment variables in `.env` with your actual values

5. Start the development server:
   ```bash
   npm run dev
   ```

## Deployment to Vercel

### Prerequisites
- Vercel account (sign up at https://vercel.com)
- Vercel CLI installed globally: `npm install -g vercel`

### Steps

1. **Build the project locally** (optional, to test):
   ```bash
   npm run build
   ```

2. **Login to Vercel**:
   ```bash
   vercel login
   ```

3. **Deploy to Vercel**:
   ```bash
   vercel
   ```
   
   Follow the prompts:
   - Set up and deploy? **Y**
   - Which scope? Choose your account
   - Link to existing project? **N** (for first deployment)
   - What's your project's name? **digital-wallet-api** (or your preferred name)
   - In which directory is your code located? **./** (current directory)

4. **Set Environment Variables** in Vercel Dashboard:
   - Go to your project in Vercel Dashboard
   - Navigate to Settings → Environment Variables
   - Add all the variables from your `.env.example` file:
     - `DATABASE_URL`
     - `JWT_SECRET`
     - `JWT_EXPIRES_IN`
     - `PORT` (Vercel will override this)
     - `INITIAL_WALLET_BALANCE`
     - `AGENT_COMMISSION_RATE`
     - `DAILY_TRANSACTION_LIMIT_AMOUNT`
     - `DAILY_TRANSACTION_LIMIT_COUNT`
     - `MONTHLY_TRANSACTION_LIMIT_AMOUNT`
     - `MONTHLY_TRANSACTION_LIMIT_COUNT`

5. **Redeploy** after setting environment variables:
   ```bash
   vercel --prod
   ```

### Important Notes

- Make sure your MongoDB database is accessible from the internet (use MongoDB Atlas for cloud hosting)
- The `JWT_SECRET` should be a strong, random string
- Vercel automatically handles the `PORT` environment variable
- Your API will be available at the URL provided by Vercel

## API Endpoints

- `GET /` - Health check
- `POST /api/v1/auth/*` - Authentication routes
- `GET/POST/PUT/DELETE /api/v1/users/*` - User management
- `GET/POST/PUT/DELETE /api/v1/wallets/*` - Wallet operations
- `GET/POST /api/v1/transactions/*` - Transaction management

## Environment Variables

See `.env.example` for all required environment variables and their descriptions.


Section 1: Authentication & Setup
These are the first requests you'll make to set up your test data.

1.1 Register a Regular User
Method: POST

Endpoint: /auth/register

Body (raw, JSON):

JSON

{
    "name": "User One",
    "email": "<EMAIL>",
    "password": "password123",
    "role": "user"
}
Action: Copy the _id of the user from the response. This is your <USER1_ID>.

1.2 Register an Agent
Method: POST

Endpoint: /auth/register

Body (raw, JSON):

JSON

{
    "name": "Agent Alpha",
    "email": "<EMAIL>",
    "password": "agentpass",
    "role": "agent"
}
Action: Copy the _id of the agent from the response. This is your <AGENT_ID>.

1.3 Login (Admin, User, Agent)
You need to perform this step to get a token for each role.

Method: POST

Endpoint: /auth/login

Body (raw, JSON):

Admin: {"email": "<EMAIL>", "password": "adminpassword"}

User: {"email": "<EMAIL>", "password": "password123"}

Agent: {"email": "<EMAIL>", "password": "agentpass"} (will fail until approved)

Action: For each successful login, copy the token from the response.

Section 2: Admin Functionalities
You must use the Admin's JWT Token in the Authorization header for these requests.

2.1 View All Users & Agents
Method: GET

Endpoint: /users

Action: Use this to get the IDs of all users, agents, and their wallets.

2.2 View All Transactions
Method: GET

Endpoint: /transactions

Action: See a list of all transactions across the system.

2.3 Approve an Agent
Method: PATCH

Endpoint: /users/approve-agent/<AGENT_ID>

Body (raw, JSON): {}

Action: This is a critical step to enable agent login and operations.

2.4 Suspend an Agent
Method: PATCH

Endpoint: /users/suspend-agent/<AGENT_ID>

Body (raw, JSON): {}

Action: Suspends an agent.

2.5 Block a User Wallet
Method: PATCH

Endpoint: /wallets/block/<USER_WALLET_ID>

Body (raw, JSON): {"isBlocked": true}

Action: Prevents the user from making transactions. Get the <USER_WALLET_ID> from a GET /users request.

Section 3: User Functionalities
You must use the User's JWT Token in the Authorization header for these requests.

3.1 Add Money (Top-up)
Method: POST

Endpoint: /wallets/add-money

Body (raw, JSON): {"amount": 5000}

Action: Add funds to the user's own wallet.

3.2 Withdraw Money
Method: POST

Endpoint: /wallets/withdraw

Body (raw, JSON): {"amount": 1000}

Action: Withdraws funds from the user's wallet.

3.3 Send Money
Method: POST

Endpoint: /wallets/send-money

Body (raw, JSON): {"receiverId": "<ANOTHER_USER_ID>", "amount": 500}

Action: Sends money to another user.

3.4 View My Transaction History
Method: GET

Endpoint: /transactions/my-history

Action: See all transactions where the user was the sender or receiver.

Section 4: Agent Functionalities
You must use the Approved Agent's JWT Token in the Authorization header for these requests.

4.1 Add Money to User (Cash-in)
Method: POST

Endpoint: /wallets/add-money

Body (raw, JSON): {"userId": "<USER_ID>", "amount": 1500}

Action: The agent adds funds to the specified user's wallet.

4.2 Withdraw Money from User (Cash-out)
Method: POST

Endpoint: /wallets/cash-out

Body (raw, JSON): {"userId": "<USER_ID>", "amount": 250}

Action: The agent withdraws funds from the specified user's wallet. The agent receives a commission.

4.3 View My Commission History
Method: GET

Endpoint: /transactions/my-history

Action: See all transactions initiated by the agent, including the commission field.

Section 5: New Features Testing
5.1 Test Daily/Monthly Limits
Prerequisite: Ensure your .env has a low limit for testing, e.g., DAILY_TRANSACTION_LIMIT_COUNT=3.

Method: POST

Endpoint: /wallets/withdraw or /wallets/send-money

Action: Send the request 3 times. The 4th attempt should return a 400 Bad Request with an error message like "Daily transaction count limit exceeded."

5.2 Test Notification System
Action: Perform any successful transaction from the lists above.

Confirmation: Look at the terminal where your server is running. A detailed transaction notification log should appear after every successful operation.
